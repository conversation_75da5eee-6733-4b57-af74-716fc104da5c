; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[env:uno]
platform = atmelavr
board = uno
framework = arduino

; 串口监视器配置
monitor_speed = 115200
monitor_filters =
    default
    time

; 编译选项
build_flags =
    -DDEBUG_MODE=1
    -Wall
    -Wextra

; 上传配置
upload_speed = 115200

; 库依赖
lib_deps =
    https://github.com/sudar/JoystickShield.git

; 调试配置
debug_tool = avr-stub
debug_build_flags = -O0 -g3 -ggdb3

; 测试配置
test_framework = unity
