# 🎮 JoystickShield PC Controller

Arduino JoystickShield-based PC game controller with joystick direction control and button mapping support.

## 🚀 Quick Start

### 1. Hardware Setup
- Arduino Uno/Nano
- JoystickShield expansion board
- USB data cable

### 2. Software Installation
```bash
pip install pyserial keyboard pywin32
```

### 3. Upload Arduino Code
Use PlatformIO or Arduino IDE to upload `src/main.cpp` to the development board

### 4. Run Controller
```bash
# Direct run
python joystick_controller_final.py

# Or use startup script
start_joystick.bat
```

## 🎯 Features

### Joystick Control (Hold Mode)
- **Up/Down/Left/Right** → Continuously hold WASD keys
- **Diagonal Movement** → Simultaneously hold two direction keys
- **Auto-release on Center** → Automatically release all direction keys when joystick returns to center

### Button Functions
- **Short Press**: Quick press and release, triggers corresponding function key
- **Long Press**: Hold for 0.3 seconds or more, triggers long press function

### Key Mapping
```
Joystick Directions:  Up→W  Down→S  Left→A  Right→D
Short Press Functions:  Joystick Button→F  Up Button→O  Down Button→J  Left Button→I  Right Button→K
Long Press Functions:  Joystick Button→Space  Direction Buttons→Arrow Keys  E→Shift  F→Ctrl
```

## 🔧 Technical Features

- **Multi-input Method Support**: Win32 API / keyboard library / pynput library
- **Auto Port Detection**: Intelligently identifies Arduino ports
- **Game Compatibility Optimization**: Supports most PC games
- **Real-time Response**: 10ms polling interval, low latency control

## ⚠️ Usage Tips

1. **Game Window**: Ensure game window is in active state
2. **Administrator Privileges**: Some games require running the program as administrator
3. **Windowed Mode**: Recommend setting game to windowed mode for best compatibility

## 📁 Project Structure

```
GameBoard/
├── src/main.cpp                    # Arduino code
├── joystick_controller_final.py    # PC controller program
├── start_joystick.bat              # Startup script
├── platformio.ini                  # PlatformIO configuration
└── README.md                       # Project documentation
```

## 🔧 Hardware Connections

```text
Joystick X-axis  -> Arduino A0    Joystick Y-axis  -> Arduino A1
Joystick Button  -> Arduino Pin 8  Up Button       -> Arduino Pin 2
Right Button     -> Arduino Pin 3  Down Button     -> Arduino Pin 4
Left Button      -> Arduino Pin 5  E Button        -> Arduino Pin 6
F Button         -> Arduino Pin 7  VCC             -> Arduino 5V
GND              -> Arduino GND
```

## 📄 License

MIT License
